@model DatVeXe.Models.BookingConfirmationViewModel
@{
    ViewData["Title"] = "Thanh toán thành công";
}

<div class="container py-4">
    <!-- Success Header -->
    <div class="text-center mb-5">
        <div class="success-animation mb-4">
            <div class="checkmark-circle">
                <div class="checkmark"></div>
            </div>
        </div>
        <h1 class="display-4 fw-bold text-success mb-3">Thanh toán thành công!</h1>
        <p class="lead text-muted">Vé của bạn đã được xác nhận và thanh toán thành công</p>
        
        @if (TempData["Success"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                <i class="bi bi-check-circle me-2"></i>
                @TempData["Success"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Payment Status Card -->
            <div class="card shadow-lg border-0 mb-4 payment-success-card">
                <div class="card-header bg-gradient-success text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-credit-card-2-front me-2"></i>Thông tin thanh toán
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="payment-info">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-receipt text-primary me-1"></i>Mã giao dịch
                                    </div>
                                    <div class="info-value transaction-code">
                                        @(Model.ThanhToan?.MaGiaoDich ?? "N/A")
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-credit-card text-info me-1"></i>Phương thức
                                    </div>
                                    <div class="info-value">
                                        <span class="badge bg-info fs-6">
                                            @(Model.ThanhToan?.PhuongThucThanhToan.GetDisplayName() ?? "N/A")
                                        </span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-clock text-warning me-1"></i>Thời gian thanh toán
                                    </div>
                                    <div class="info-value">
                                        @(Model.ThanhToan?.NgayThanhToan?.ToString("dd/MM/yyyy HH:mm:ss") ?? DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"))
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="amount-summary">
                                <div class="amount-breakdown">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Giá vé:</span>
                                        <span>@string.Format("{0:N0}", Model.GiaVe) VNĐ</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Phí dịch vụ:</span>
                                        <span>0 VNĐ</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold fs-5 text-success">
                                        <span>Tổng thanh toán:</span>
                                        <span>@string.Format("{0:N0}", Model.GiaVe) VNĐ</span>
                                    </div>
                                </div>
                                
                                <div class="payment-status-badge mt-3 text-center">
                                    <span class="badge bg-success fs-6 px-3 py-2">
                                        <i class="bi bi-check-circle me-1"></i>Đã thanh toán
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ticket Information -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-ticket-perforated me-2"></i>Thông tin vé xe
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            <div class="ticket-info">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-hash text-primary me-1"></i>Mã vé
                                    </div>
                                    <div class="info-value ticket-code">@Model.MaVe</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-person text-warning me-1"></i>Hành khách
                                    </div>
                                    <div class="info-value">@Model.TenKhach</div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-telephone text-info me-1"></i>Số điện thoại
                                    </div>
                                    <div class="info-value">@Model.SoDienThoai</div>
                                </div>

                                @if (!string.IsNullOrEmpty(Model.Email))
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-envelope text-secondary me-1"></i>Email
                                        </div>
                                        <div class="info-value">@Model.Email</div>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            <div class="trip-info">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-geo-alt text-success me-1"></i>Tuyến đường
                                    </div>
                                    <div class="info-value route-display">
                                        @Model.ChuyenXe.DiemDiDisplay
                                        <i class="bi bi-arrow-right mx-2"></i>
                                        @Model.ChuyenXe.DiemDenDisplay
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-calendar-event text-primary me-1"></i>Thời gian khởi hành
                                    </div>
                                    <div class="info-value">
                                        <div class="fw-bold">@Model.ChuyenXe.NgayKhoiHanh.ToString("dddd, dd/MM/yyyy")</div>
                                        <div class="text-success fs-5">@Model.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="bi bi-truck text-warning me-1"></i>Phương tiện
                                    </div>
                                    <div class="info-value">
                                        @Model.ChuyenXe.Xe?.LoaiXe
                                        <small class="text-muted d-block">@Model.ChuyenXe.Xe?.BienSo</small>
                                    </div>
                                </div>

                                @if (Model.ChoNgoi != null)
                                {
                                    <div class="info-item">
                                        <div class="info-label">
                                            <i class="bi bi-square-fill text-success me-1"></i>Ghế ngồi
                                        </div>
                                        <div class="info-value">
                                            <span class="badge bg-success fs-6">Ghế @Model.ChoNgoi.SoGhe</span>
                                            <small class="text-muted d-block">@Model.ChoNgoi.LoaiGhe</small>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-qr-code me-2"></i>Mã QR vé
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-container">
                        <div id="qrcode" class="mb-3"></div>
                        <p class="text-muted">Quét mã QR này khi lên xe</p>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-list-check me-2"></i>Bước tiếp theo
                    </h6>
                </div>
                <div class="card-body">
                    <div class="steps-timeline">
                        <div class="step completed">
                            <div class="step-icon">
                                <i class="bi bi-check-circle-fill"></i>
                            </div>
                            <div class="step-content">
                                <h6>Thanh toán thành công</h6>
                                <p class="text-muted mb-0">Vé đã được xác nhận và thanh toán</p>
                            </div>
                        </div>
                        
                        <div class="step">
                            <div class="step-icon">
                                <i class="bi bi-envelope"></i>
                            </div>
                            <div class="step-content">
                                <h6>Nhận thông báo xác nhận</h6>
                                <p class="text-muted mb-0">Email và SMS xác nhận sẽ được gửi đến bạn</p>
                            </div>
                        </div>
                        
                        <div class="step">
                            <div class="step-icon">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                            <div class="step-content">
                                <h6>Đến bến xe</h6>
                                <p class="text-muted mb-0">Có mặt tại bến xe trước 15 phút</p>
                            </div>
                        </div>
                        
                        <div class="step">
                            <div class="step-icon">
                                <i class="bi bi-bus-front"></i>
                            </div>
                            <div class="step-content">
                                <h6>Lên xe</h6>
                                <p class="text-muted mb-0">Xuất trình mã vé hoặc QR code</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="@Url.Action("Search")" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle me-2"></i>Đặt vé mới
                    </a>
                    @if (Context.Session.GetInt32("UserId") != null)
                    {
                        <a href="@Url.Action("Index", "MyTickets")" class="btn btn-outline-primary btn-lg">
                            <i class="bi bi-ticket-perforated me-2"></i>Vé của tôi
                        </a>
                    }
                    <button type="button" class="btn btn-outline-success btn-lg" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>In vé
                    </button>
                    <button type="button" class="btn btn-outline-info btn-lg" onclick="downloadTicket()">
                        <i class="bi bi-download me-2"></i>Tải vé
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include QR Code library -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

<style>
    .success-animation {
        position: relative;
        display: inline-block;
    }

    .checkmark-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(45deg, #28a745, #20c997);
        position: relative;
        margin: 0 auto;
        animation: scaleIn 0.5s ease-in-out;
    }

    .checkmark {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 30px;
        height: 30px;
    }

    .checkmark::before {
        content: '';
        position: absolute;
        width: 15px;
        height: 8px;
        border-left: 3px solid white;
        border-bottom: 3px solid white;
        transform: rotate(-45deg);
        top: 8px;
        left: 8px;
        animation: checkmark 0.5s ease-in-out 0.5s both;
    }

    @keyframes scaleIn {
        0% {
            transform: scale(0);
        }
        100% {
            transform: scale(1);
        }
    }

    @keyframes checkmark {
        0% {
            width: 0;
            height: 0;
        }
        100% {
            width: 15px;
            height: 8px;
        }
    }

    .bg-gradient-success {
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .payment-success-card {
        border-left: 5px solid #28a745;
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .info-item {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-label {
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .info-value {
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
    }

    .ticket-code, .transaction-code {
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        color: #007bff;
        background: #f8f9ff;
        padding: 0.5rem;
        border-radius: 5px;
        border: 2px dashed #007bff;
    }

    .route-display {
        font-size: 1.1rem;
        color: #28a745;
    }

    .amount-breakdown {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
    }

    .qr-code-container {
        padding: 1rem;
    }

    .steps-timeline {
        position: relative;
    }

    .step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 15px;
        top: 35px;
        width: 2px;
        height: 40px;
        background: #dee2e6;
    }

    .step.completed::after {
        background: #28a745;
    }

    .step-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .step.completed .step-icon {
        background: #28a745;
        color: white;
    }

    .step-content h6 {
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .step-content p {
        font-size: 0.85rem;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    @media print {
        .btn, .card-header {
            display: none !important;
        }

        .card {
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Generate QR Code
        const ticketCode = '@Model.MaVe';
        const qrData = JSON.stringify({
            ticketCode: ticketCode,
            passengerName: '@Model.TenKhach',
            phone: '@Model.SoDienThoai',
            tripId: '@Model.ChuyenXe.ChuyenXeId',
            seatNumber: '@(Model.ChoNgoi?.SoGhe ?? "")',
            departureTime: '@Model.ChuyenXe.NgayKhoiHanh.ToString("yyyy-MM-dd HH:mm:ss")'
        });

        QRCode.toCanvas(document.getElementById('qrcode'), qrData, {
            width: 200,
            height: 200,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        }, function (error) {
            if (error) console.error(error);
        });
    });

    function downloadTicket() {
        // Create a printable version
        const printWindow = window.open('', '_blank');
        const ticketHtml = `
            <html>
                <head>
                    <title>Vé xe - ${@Model.MaVe}</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .ticket { border: 2px solid #000; padding: 20px; max-width: 600px; margin: 0 auto; }
                        .header { text-align: center; margin-bottom: 20px; }
                        .info { margin-bottom: 10px; }
                        .qr-code { text-align: center; margin: 20px 0; }
                    </style>
                </head>
                <body>
                    <div class="ticket">
                        <div class="header">
                            <h2>VÉ XE KHÁCH</h2>
                            <h3>Mã vé: ${@Model.MaVe}</h3>
                        </div>
                        <div class="info"><strong>Hành khách:</strong> ${@Model.TenKhach}</div>
                        <div class="info"><strong>Số điện thoại:</strong> ${@Model.SoDienThoai}</div>
                        <div class="info"><strong>Tuyến:</strong> ${@Model.ChuyenXe.DiemDiDisplay} → ${@Model.ChuyenXe.DiemDenDisplay}</div>
                        <div class="info"><strong>Thời gian:</strong> ${@Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")}</div>
                        <div class="info"><strong>Ghế:</strong> ${@(Model.ChoNgoi?.SoGhe ?? "N/A")}</div>
                        <div class="info"><strong>Giá vé:</strong> ${@string.Format("{0:N0}", Model.GiaVe)} VNĐ</div>
                        <div class="qr-code">
                            <canvas id="ticketQR"></canvas>
                        </div>
                    </div>
                </body>
            </html>
        `;
        
        printWindow.document.write(ticketHtml);
        printWindow.document.close();
        printWindow.print();
    }
</script>
