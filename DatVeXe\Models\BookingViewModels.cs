using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace DatVeXe.Models
{
    // ViewModels cho quy trình đặt vé hoàn chỉnh
    public class BookingStepViewModel
    {
        public int CurrentStep { get; set; } = 1;
        public int TotalSteps { get; set; } = 4;
        public string SessionId { get; set; } = string.Empty;
        
        // Step 1: Chọn chuyến xe
        public int? ChuyenXeId { get; set; }
        public ChuyenXe? ChuyenXe { get; set; }
        
        // Step 2: Chọn ghế
        public int? ChoNgoiId { get; set; }
        public ChoNgoi? ChoNgoi { get; set; }
        
        // Step 3: Thông tin hành khách
        [Required(ErrorMessage = "Vui lòng nhập tên khách hàng")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên khách hàng phải từ 2 đến 100 ký tự")]
        [Display(Name = "Tên khách hàng")]
        public string TenKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [RegularExpression(@"^(0[0-9]{9})$", ErrorMessage = "Số điện thoại không hợp lệ (phải bắt đầu bằng số 0 và có 10 số)")]
        [Display(Name = "Số điện thoại")]
        public string SoDienThoai { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }
        
        // Step 4: Thanh toán
        [Required(ErrorMessage = "Vui lòng chọn phương thức thanh toán")]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }

        public decimal TongTien { get; set; }

        // Kết quả
        public string? MaVe { get; set; }
        public string? MaGiaoDich { get; set; }

        // Thêm các thuộc tính cần thiết
        public DateTime NgayDat { get; set; } = DateTime.Now;
        public TrangThaiVe TrangThai { get; set; } = TrangThaiVe.DaDat;
        public int? NguoiDungId { get; set; }

        // Validation methods
        public bool IsStep1Valid()
        {
            return ChuyenXeId.HasValue;
        }

        public bool IsStep2Valid()
        {
            return IsStep1Valid() && ChoNgoiId.HasValue;
        }

        public bool IsStep3Valid()
        {
            return IsStep2Valid() &&
                   !string.IsNullOrWhiteSpace(TenKhach) &&
                   !string.IsNullOrWhiteSpace(SoDienThoai);
        }

        public bool IsStep4Valid()
        {
            return IsStep3Valid() && PhuongThucThanhToan != 0;
        }
    }

    public class BookingConfirmationViewModel
    {
        public string MaVe { get; set; } = string.Empty;
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public decimal GiaVe { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi? ChoNgoi { get; set; }
        public ThanhToan? ThanhToan { get; set; }
        public string QRCodeData { get; set; } = string.Empty;
        public bool EmailSent { get; set; }
        public bool SMSSent { get; set; }
        public string BookingReference { get; set; } = string.Empty;
    }

    public class PaymentRequestViewModel
    {
        [Required]
        public int VeId { get; set; }
        
        [Required]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThuc { get; set; }
        
        [Required]
        [Display(Name = "Số tiền")]
        public decimal SoTien { get; set; }
        
        public string? ReturnUrl { get; set; }
        public string? CancelUrl { get; set; }
        public string? OrderInfo { get; set; }
    }

    public class PaymentResponseViewModel
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? MaGiaoDich { get; set; }
        public string? PaymentUrl { get; set; }
        public TrangThaiThanhToan TrangThai { get; set; }
        public DateTime? ThoiGianThanhToan { get; set; }
        public string? ErrorCode { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class BookingSearchViewModel
    {
        [Required(ErrorMessage = "Vui lòng chọn điểm đi")]
        [Display(Name = "Điểm đi")]
        public string DiemDi { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng chọn điểm đến")]
        [Display(Name = "Điểm đến")]
        public string DiemDen { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng chọn ngày đi")]
        [Display(Name = "Ngày đi")]
        [DataType(DataType.Date)]
        public DateTime NgayDi { get; set; } = DateTime.Today.AddDays(1);

        [Display(Name = "Số hành khách")]
        [Range(1, 10, ErrorMessage = "Số hành khách từ 1 đến 10")]
        public int SoHanhKhach { get; set; } = 1;

        // Bộ lọc nâng cao
        [Display(Name = "Giờ khởi hành từ")]
        [DataType(DataType.Time)]
        public TimeSpan? GioKhoiHanhTu { get; set; }

        [Display(Name = "Giờ khởi hành đến")]
        [DataType(DataType.Time)]
        public TimeSpan? GioKhoiHanhDen { get; set; }

        [Display(Name = "Loại xe")]
        public string? LoaiXe { get; set; }

        [Display(Name = "Nhà xe")]
        public string? NhaXe { get; set; }

        [Display(Name = "Giá từ")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal? GiaTu { get; set; }

        [Display(Name = "Giá đến")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal? GiaDen { get; set; }

        [Display(Name = "Sắp xếp theo")]
        public string SortBy { get; set; } = "NgayKhoiHanh"; // NgayKhoiHanh, Gia, NhaXe

        [Display(Name = "Thứ tự")]
        public string SortOrder { get; set; } = "asc"; // asc, desc

        // Kết quả tìm kiếm
        public List<ChuyenXe> KetQua { get; set; } = new List<ChuyenXe>();
        public int TotalResults { get; set; }
        public bool HasSearched { get; set; }
    }

    public class SeatSelectionViewModel
    {
        public int ChuyenXeId { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public Xe Xe { get; set; } = new Xe();
        public List<ChoNgoiViewModel> DanhSachGhe { get; set; } = new List<ChoNgoiViewModel>();
        public int? SelectedSeatId { get; set; }
        public string SessionId { get; set; } = string.Empty;
        
        // Thông tin layout ghế
        public int SoHang { get; set; }
        public int SoCot { get; set; }
        public string LoaiXe { get; set; } = string.Empty;
    }

    public class PassengerInfoViewModel
    {
        public int ChuyenXeId { get; set; }
        public int ChoNgoiId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên khách hàng")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên khách hàng phải từ 2 đến 100 ký tự")]
        [Display(Name = "Tên khách hàng")]
        public string TenKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [RegularExpression(@"^(0[0-9]{9})$", ErrorMessage = "Số điện thoại không hợp lệ")]
        [Display(Name = "Số điện thoại")]
        public string SoDienThoai { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }

        [Display(Name = "Nhận thông báo qua SMS")]
        public bool NhanSMS { get; set; } = true;

        [Display(Name = "Nhận thông báo qua Email")]
        public bool NhanEmail { get; set; } = true;

        // Navigation properties - chỉ để hiển thị, không bind
        public ChuyenXe? ChuyenXe { get; set; }
        public ChoNgoi? ChoNgoi { get; set; }
    }

    public class PaymentSelectionViewModel
    {
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? GhiChu { get; set; }
        public decimal GiaVe { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi ChoNgoi { get; set; } = new ChoNgoi();

        [Required(ErrorMessage = "Vui lòng chọn phương thức thanh toán")]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }

        public decimal TongTien { get; set; }
        public decimal PhiDichVu { get; set; } = 0;
        public decimal ThanhTien { get; set; }

        // Thông tin khuyến mãi
        public string? MaKhuyenMai { get; set; }
        public decimal GiamGia { get; set; } = 0;

        // Điều khoản
        [Required(ErrorMessage = "Vui lòng đồng ý với điều khoản sử dụng")]
        [Display(Name = "Tôi đồng ý với điều khoản sử dụng")]
        public bool DongYDieuKhoan { get; set; }
    }

    public class BookingSummaryViewModel
    {
        public string MaVe { get; set; } = string.Empty;
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi ChoNgoi { get; set; } = new ChoNgoi();
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public decimal TongTien { get; set; }
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }
        public TrangThaiThanhToan TrangThaiThanhToan { get; set; }
        public DateTime NgayDat { get; set; }
        public string? GhiChu { get; set; }
    }

    // ViewModel cho danh bạ hành khách
    public class ContactListViewModel
    {
        public List<DanhBaHanhKhach> DanhSachLienHe { get; set; } = new List<DanhBaHanhKhach>();
        public string? SearchTerm { get; set; }
        public int TotalContacts { get; set; }
    }

    // ViewModel cho quản lý vé của tôi
    public class MyTicketsViewModel
    {
        public List<Ve> VeDaDat { get; set; } = new List<Ve>();
        public List<Ve> VeSapDi { get; set; } = new List<Ve>();
        public List<Ve> VeDaHoanThanh { get; set; } = new List<Ve>();
        public List<Ve> VeDaHuy { get; set; } = new List<Ve>();

        public int TongVe { get; set; }
        public decimal TongChiTieu { get; set; }
        public int VeChuaDanhGia { get; set; }

        // Bộ lọc
        public string? TrangThaiLoc { get; set; }
        public DateTime? TuNgay { get; set; }
        public DateTime? DenNgay { get; set; }
        public string? SearchTerm { get; set; }
    }

    // ViewModel cho chi tiết vé
    public class TicketDetailViewModel
    {
        public Ve Ve { get; set; } = new Ve();
        public string QRCodeData { get; set; } = string.Empty;
        public bool CanCancel { get; set; }
        public bool CanModify { get; set; }
        public bool CanReview { get; set; }
        public DanhGiaChuyenDi? DanhGia { get; set; }
        public ThanhToan? ThanhToan { get; set; }

        // Thông tin bổ sung
        public string TrangThaiDisplay { get; set; } = string.Empty;
        public string DiemDonTra { get; set; } = string.Empty;
        public TimeSpan ThoiGianConLai { get; set; }
    }

    // ViewModel cho đánh giá chuyến đi
    public class ReviewTripViewModel
    {
        public int VeId { get; set; }
        public Ve Ve { get; set; } = new Ve();

        [Required]
        [Range(1, 5, ErrorMessage = "Vui lòng chọn điểm đánh giá từ 1 đến 5")]
        [Display(Name = "Điểm đánh giá tổng thể")]
        public int DiemDanhGia { get; set; }

        [StringLength(1000)]
        [Display(Name = "Nội dung đánh giá")]
        public string? NoiDungDanhGia { get; set; }

        [Range(1, 5)]
        [Display(Name = "Đánh giá tài xế")]
        public int? DanhGiaTaiXe { get; set; }

        [Range(1, 5)]
        [Display(Name = "Đánh giá xe")]
        public int? DanhGiaXe { get; set; }

        [Range(1, 5)]
        [Display(Name = "Đánh giá dịch vụ")]
        public int? DanhGiaDichVu { get; set; }

        [Display(Name = "Có khuyến nghị cho người khác")]
        public bool CoKhuyenNghi { get; set; } = true;
    }

    // ViewModel cho trang thanh toán thất bại
    public class PaymentFailureViewModel
    {
        public string? ErrorCode { get; set; }
        public string? ErrorMessage { get; set; }
        public string? TransactionId { get; set; }
        public string? PaymentMethod { get; set; }
        public decimal? Amount { get; set; }
        public string? SessionId { get; set; }
        public BookingFailureInfo? BookingInfo { get; set; }
    }

    // ViewModel cho thông tin booking khi thanh toán thất bại
    public class BookingFailureInfo
    {
        public string MaVe { get; set; } = string.Empty;
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string DiemDi { get; set; } = string.Empty;
        public string DiemDen { get; set; } = string.Empty;
        public DateTime? NgayKhoiHanh { get; set; }
        public string? SoGhe { get; set; }
        public decimal GiaVe { get; set; }
    }

    // ViewModel cho xác nhận booking
    public class BookingConfirmationViewModel
    {
        public string MaVe { get; set; } = string.Empty;
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public decimal GiaVe { get; set; }
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public ChoNgoi? ChoNgoi { get; set; }
        public ThanhToan? ThanhToan { get; set; }
        public bool EmailSent { get; set; }
        public bool SMSSent { get; set; }
        public string? QRCodeData { get; set; }
    }

    // ViewModel cho payment request
    public class PaymentRequestViewModel
    {
        public int VeId { get; set; }
        public decimal SoTien { get; set; }
        public PhuongThucThanhToan PhuongThuc { get; set; }
        public string? OrderInfo { get; set; }
        public string? ReturnUrl { get; set; }
        public string? CancelUrl { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerPhone { get; set; }
        public string? CustomerEmail { get; set; }
    }

    // ViewModel cho payment response
    public class PaymentResponseViewModel
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? PaymentUrl { get; set; }
        public string? MaGiaoDich { get; set; }
        public TrangThaiThanhToan TrangThai { get; set; }
        public DateTime? ThoiGianThanhToan { get; set; }
        public string? ErrorCode { get; set; }
        public Dictionary<string, string>? AdditionalData { get; set; }
    }
}
